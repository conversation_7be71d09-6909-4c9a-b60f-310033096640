<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>D3.js 图形组件演示</h1>
      <p>这是使用 D3.js 重新实现的图形可视化组件，具有与 Cytoscape 版本相同的功能</p>

      <div class="demo-controls">
        <el-select v-model="selectedDataSet" placeholder="选择测试数据" @change="loadTestData">
          <el-option label="基础数据" value="basic" />
          <el-option label="层级数据" value="hierarchical" />
          <el-option label="网状数据" value="network" />
          <el-option label="随机数据" value="random" />
          <el-option label="大数据集" value="large" />
          <el-option label="单节点" value="single" />
          <el-option label="空数据" value="empty" />
        </el-select>
        <el-button @click="addRandomNode" type="success">添加随机节点</el-button>
        <el-button @click="clearGraph" type="danger">清空图形</el-button>
        <el-switch v-model="panelVisible" active-text="显示信息面板" inactive-text="隐藏信息面板" />
      </div>
    </div>

    <div class="demo-content">
      <D3Graph
        ref="graphRef"
        :data="graphData"
        :attrs="currentAttrs"
        v-model:panel-visible="panelVisible"
        @update:panel="handlePanelUpdate"
        @load-children="handleLoadChildren"
      />
    </div>

    <div class="demo-info">
      <el-card>
        <template #header>
          <span>功能特性</span>
        </template>
        <ul>
          <li><strong>多种布局</strong>：同心圆布局、垂直树布局、水平树布局</li>
          <li><strong>交互功能</strong>：拖拽节点、缩放视图、点击高亮、双击展开/折叠</li>
          <li><strong>动态数据</strong>：支持动态加载子节点、实时更新图形</li>
          <li><strong>视觉效果</strong>：节点颜色映射、高亮效果、淡化效果</li>
          <li><strong>工具栏</strong>：回到中心、布局切换、全屏模式</li>
          <li><strong>右键菜单</strong>：显示节点详细信息</li>
        </ul>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import D3Graph from "@/components/graph/d3-graph.vue";
import { getTestData, validateGraphData } from "@/utils/graph-test-data.js";

const graphRef = ref(null);
const panelVisible = ref(false);
const currentAttrs = ref([]);
const selectedDataSet = ref("basic");

// 示例图形数据
const graphData = ref({
  nodes: [],
  links: [],
});

// 加载测试数据
function loadTestData(dataType = selectedDataSet.value) {
  try {
    const data = getTestData(dataType);
    const errors = validateGraphData(data);

    if (errors.length > 0) {
      console.error("数据验证失败:", errors);
      return;
    }

    graphData.value = data;
    console.log(`已加载${dataType}测试数据`);
  } catch (error) {
    console.error("加载数据失败:", error);
  }
}

// 添加随机节点
function addRandomNode() {
  const nodeId = `node_${Date.now()}`;
  const nodeTypes = ["person", "unit", "train", "other"];
  const nodeType = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];

  const newNode = {
    id: nodeId,
    nodeName: `随机节点_${nodeId.slice(-4)}`,
    labels: [nodeType],
    attrs: [
      { code: "name", value: `随机节点_${nodeId.slice(-4)}` },
      { code: "type", value: nodeType },
      { code: "created", value: new Date().toLocaleString() },
    ],
  };

  // 随机连接到现有节点
  if (graphData.value.nodes.length > 0) {
    const randomExistingNode = graphData.value.nodes[Math.floor(Math.random() * graphData.value.nodes.length)];

    const newLink = {
      source: randomExistingNode.id,
      target: nodeId,
      name: "连接",
    };

    graphData.value.links.push(newLink);
  }

  graphData.value.nodes.push(newNode);
}

// 清空图形
function clearGraph() {
  graphData.value = {
    nodes: [],
    links: [],
  };
}

// 处理面板更新
function handlePanelUpdate(nodeData) {
  currentAttrs.value = nodeData.attrs || [];
}

// 处理加载子节点
function handleLoadChildren({ id, raw }) {
  console.log("加载子节点:", id, raw);

  // 模拟异步加载子节点
  setTimeout(() => {
    const childNodes = [
      {
        id: `${id}_child_1`,
        nodeName: `${raw.nodeName || id}的子节点1`,
        labels: ["other"],
        attrs: [
          { code: "name", value: `${raw.nodeName || id}的子节点1` },
          { code: "parent", value: raw.nodeName || id },
        ],
      },
      {
        id: `${id}_child_2`,
        nodeName: `${raw.nodeName || id}的子节点2`,
        labels: ["other"],
        attrs: [
          { code: "name", value: `${raw.nodeName || id}的子节点2` },
          { code: "parent", value: raw.nodeName || id },
        ],
      },
    ];

    const childLinks = [
      { source: id, target: `${id}_child_1`, name: "包含" },
      { source: id, target: `${id}_child_2`, name: "包含" },
    ];

    // 调用图形组件的 applyChildren 方法
    if (graphRef.value) {
      graphRef.value.applyChildren(id, {
        nodes: childNodes,
        links: childLinks,
      });
    }
  }, 500);
}

// 初始化加载示例数据
loadTestData("basic");
</script>

<style scoped lang="scss">
.demo-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.demo-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;

  h1 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
  }

  p {
    margin: 0 0 16px 0;
    color: #606266;
    font-size: 14px;
  }
}

.demo-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.demo-content {
  flex: 1;
  position: relative;
  min-height: 0;
}

.demo-info {
  padding: 20px;
  background: white;
  border-top: 1px solid #e4e7ed;

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      color: #606266;
      line-height: 1.5;

      strong {
        color: #303133;
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-header {
    padding: 16px;

    h1 {
      font-size: 20px;
    }
  }

  .demo-controls {
    flex-direction: column;
    align-items: stretch;

    .el-button {
      width: 100%;
    }
  }

  .demo-info {
    padding: 16px;
  }
}
</style>
