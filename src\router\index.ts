import { createRouter, createWeb<PERSON>ashH<PERSON>ory, RouteRecordRaw } from "vue-router";
import { usePermissStore } from "../store/permiss";
import Home from "../views/home.vue";
import NProgress from "nprogress";
import "nprogress/nprogress.css";

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/dashboard",
  },
  {
    path: "/",
    name: "Home",
    component: Home,
    children: [
      {
        path: "/dashboard",
        name: "dashboard",
        meta: {
          title: "系统首页",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "dashboard" */ "../views/dashboard.vue"),
      },
      {
        path: "/graph",
        name: "graph",
        meta: {
          title: "知识图谱",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/graph.vue"),
      },
      {
        path: "/graph1",
        name: "graph1",
        meta: {
          title: "图谱布局测试",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/graph-layout-test.vue"),
      },
      {
        path: "/d3-graph-demo",
        name: "d3-graph-demo",
        meta: {
          title: "D3图形组件演示",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "d3-graph" */ "../views/d3-graph-demo.vue"),
      },
      {
        path: "/openLayers",
        name: "openLayers",
        meta: {
          title: "openLayers",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "openLayers" */ "../views/openLayers/index.vue"),
      },
      {
        path: "/TreeDemo",
        name: "TreeDemo",
        meta: {
          title: "知识图谱",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/TreeDemo.vue"),
      },
      {
        path: "/RPArea",
        name: "RPArea",
        meta: {
          title: "通信普通覆盖范围",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/RPArea.vue"),
      },
      {
        path: "/RadarDetectionRange",
        name: "RadarDetectionRange",
        meta: {
          title: "雷达覆盖范围",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/RadarDetectionRange.vue"),
      },
      {
        path: "/SkyWaveSingleLink",
        name: "SkyWaveSingleLink",
        meta: {
          title: "无人机传播",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/SkyWaveSingleLink.vue"),
      },
      {
        path: "/VideoStreamPlayer",
        name: "VideoStreamPlayer",
        meta: {
          title: "视频流播放器",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/VideoStreamPlayer.vue"),
      },
      {
        path: "/WebSocketFlvPlayer",
        name: "WebSocketFlvPlayer",
        meta: {
          title: "WebSocket FLV播放器",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/WebSocketFlvPlayer.vue"),
      },
      {
        path: "/FlvPlayerTest",
        name: "FlvPlayerTest",
        meta: {
          title: "FLV播放器测试",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/FlvPlayerTest.vue"),
      },
      {
        path: "/SimpleFlvPlayer",
        name: "SimpleFlvPlayer",
        meta: {
          title: "简单FLV播放器",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/SimpleFlvPlayer.vue"),
      },
      {
        path: "/BinaryFlvPlayer",
        name: "BinaryFlvPlayer",
        meta: {
          title: "二进制FLV播放器",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/BinaryFlvPlayer.vue"),
      },
      {
        path: "/MultiFormatPlayer",
        name: "MultiFormatPlayer",
        meta: {
          title: "多格式播放器",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/MultiFormatPlayer.vue"),
      },
      {
        path: "/ResizeDemo",
        name: "ResizeDemo",
        meta: {
          title: "可缩放元素",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "video" */ "../views/ResizeDemo.vue"),
      },
      {
        path: "/RPSingleLink",
        name: "RPSingleLink",
        meta: {
          title: "通信单链路覆盖范围",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/RPSingleLink.vue"),
      },
      {
        path: "/RPDataLink",
        name: "RPDataLink",
        meta: {
          title: "通信数据链链路覆盖范围",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/RPDataLink.vue"),
      },
      {
        path: "/UVAControl",
        name: "UVAControl",
        meta: {
          title: "无人机控制",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/UVAControl/index.vue"),
      },
      {
        path: "/UVARTSPControl",
        name: "UVARTSPControl",
        meta: {
          title: "无人机控制",
          noAuth: true,
        },
        component: () => import(/* webpackChunkName: "graph" */ "../views/UVARTSPControl/index.vue"),
      },
      {
        path: "/system-user",
        name: "system-user",
        meta: {
          title: "用户管理",
          permiss: "11",
        },
        component: () => import(/* webpackChunkName: "system-user" */ "../views/system/user.vue"),
      },
      {
        path: "/system-role",
        name: "system-role",
        meta: {
          title: "角色管理",
          permiss: "12",
        },
        component: () => import(/* webpackChunkName: "system-role" */ "../views/system/role.vue"),
      },
      {
        path: "/system-menu",
        name: "system-menu",
        meta: {
          title: "菜单管理",
          permiss: "13",
        },
        component: () => import(/* webpackChunkName: "system-menu" */ "../views/system/menu.vue"),
      },
      {
        path: "/table",
        name: "basetable",
        meta: {
          title: "基础表格",
          permiss: "31",
        },
        component: () => import(/* webpackChunkName: "table" */ "../views/table/basetable.vue"),
      },
      {
        path: "/table-editor",
        name: "table-editor",
        meta: {
          title: "可编辑表格",
          permiss: "32",
        },
        component: () => import(/* webpackChunkName: "table-editor" */ "../views/table/table-editor.vue"),
      },
      {
        path: "/schart",
        name: "schart",
        meta: {
          title: "schart图表",
          permiss: "41",
        },
        component: () => import(/* webpackChunkName: "schart" */ "../views/chart/schart.vue"),
      },
      {
        path: "/echarts",
        name: "echarts",
        meta: {
          title: "echarts图表",
          permiss: "42",
        },
        component: () => import(/* webpackChunkName: "echarts" */ "../views/chart/echarts.vue"),
      },

      {
        path: "/icon",
        name: "icon",
        meta: {
          title: "图标",
          permiss: "5",
        },
        component: () => import(/* webpackChunkName: "icon" */ "../views/pages/icon.vue"),
      },
      {
        path: "/ucenter",
        name: "ucenter",
        meta: {
          title: "个人中心",
        },
        component: () => import(/* webpackChunkName: "ucenter" */ "../views/pages/ucenter.vue"),
      },
      {
        path: "/editor",
        name: "editor",
        meta: {
          title: "富文本编辑器",
          permiss: "291",
        },
        component: () => import(/* webpackChunkName: "editor" */ "../views/pages/editor.vue"),
      },
      {
        path: "/markdown",
        name: "markdown",
        meta: {
          title: "markdown编辑器",
          permiss: "292",
        },
        component: () => import(/* webpackChunkName: "markdown" */ "../views/pages/markdown.vue"),
      },
      {
        path: "/export",
        name: "export",
        meta: {
          title: "导出Excel",
          permiss: "34",
        },
        component: () => import(/* webpackChunkName: "export" */ "../views/table/export.vue"),
      },
      {
        path: "/import",
        name: "import",
        meta: {
          title: "导入Excel",
          permiss: "33",
        },
        component: () => import(/* webpackChunkName: "import" */ "../views/table/import.vue"),
      },
      {
        path: "/theme",
        name: "theme",
        meta: {
          title: "主题设置",
          permiss: "7",
        },
        component: () => import(/* webpackChunkName: "theme" */ "../views/pages/theme.vue"),
      },
      {
        path: "/calendar",
        name: "calendar",
        meta: {
          title: "日历",
          permiss: "24",
        },
        component: () => import(/* webpackChunkName: "calendar" */ "../views/element/calendar.vue"),
      },
      {
        path: "/watermark",
        name: "watermark",
        meta: {
          title: "水印",
          permiss: "25",
        },
        component: () => import(/* webpackChunkName: "watermark" */ "../views/element/watermark.vue"),
      },
      {
        path: "/carousel",
        name: "carousel",
        meta: {
          title: "走马灯",
          permiss: "23",
        },
        component: () => import(/* webpackChunkName: "carousel" */ "../views/element/carousel.vue"),
      },
      {
        path: "/tour",
        name: "tour",
        meta: {
          title: "分步引导",
          permiss: "26",
        },
        component: () => import(/* webpackChunkName: "tour" */ "../views/element/tour.vue"),
      },
      {
        path: "/steps",
        name: "steps",
        meta: {
          title: "步骤条",
          permiss: "27",
        },
        component: () => import(/* webpackChunkName: "steps" */ "../views/element/steps.vue"),
      },
      {
        path: "/form",
        name: "forms",
        meta: {
          title: "表单",
          permiss: "21",
        },
        component: () => import(/* webpackChunkName: "form" */ "../views/element/form.vue"),
      },
      {
        path: "/upload",
        name: "upload",
        meta: {
          title: "上传",
          permiss: "22",
        },
        component: () => import(/* webpackChunkName: "upload" */ "../views/element/upload.vue"),
      },
      {
        path: "/statistic",
        name: "statistic",
        meta: {
          title: "统计",
          permiss: "28",
        },
        component: () => import(/* webpackChunkName: "statistic" */ "../views/element/statistic.vue"),
      },
    ],
  },
  {
    path: "/login",
    meta: {
      title: "登录",
      noAuth: true,
    },
    component: () => import(/* webpackChunkName: "login" */ "../views/pages/login.vue"),
  },
  {
    path: "/register",
    meta: {
      title: "注册",
      noAuth: true,
    },
    component: () => import(/* webpackChunkName: "register" */ "../views/pages/register.vue"),
  },
  {
    path: "/reset-pwd",
    meta: {
      title: "重置密码",
      noAuth: true,
    },
    component: () => import(/* webpackChunkName: "reset-pwd" */ "../views/pages/reset-pwd.vue"),
  },
  {
    path: "/403",
    meta: {
      title: "没有权限",
      noAuth: true,
    },
    component: () => import(/* webpackChunkName: "403" */ "../views/pages/403.vue"),
  },
  {
    path: "/404",
    meta: {
      title: "找不到页面",
      noAuth: true,
    },
    component: () => import(/* webpackChunkName: "404" */ "../views/pages/404.vue"),
  },
  { path: "/:path(.*)", redirect: "/404" },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  NProgress.start();
  const role = localStorage.getItem("vuems_name");
  const permiss = usePermissStore();

  if (!role && to.meta.noAuth !== true) {
    next("/login");
  } else if (typeof to.meta.permiss == "string" && !permiss.key.includes(to.meta.permiss)) {
    // 如果没有权限，则进入403
    next("/403");
  } else {
    next();
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
