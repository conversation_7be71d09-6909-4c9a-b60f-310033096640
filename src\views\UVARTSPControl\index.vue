<template>
  <RtspDialog v-model="showRtspFlag" :rtsp-list="rtspList" />
</template>

<script setup>
import { ref } from "vue";
import RtspDialog from "../UVAControl/components/RtspGimbalConsole.vue";

const showRtspFlag = ref(true);
const rtspList = ref([
  {
    id: 1,
    name: "无人机1",
    rtspKJG: "rtsp://rtspstream:<EMAIL>/movie",
  },
  {
    id: 2,
    name: "无人机2",
    rtspKJG: "rtsp://rtspstream:<EMAIL>/movie",
  },
]);
</script>

<style></style>
