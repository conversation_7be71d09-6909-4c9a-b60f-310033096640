# 图形组件使用指南

## 概述

本项目提供了两个功能相同的图形可视化组件：

1. **Cytoscape版本** (`src/components/graph/index.vue`) - 基于Cytoscape.js
2. **D3版本** (`src/components/graph/d3-graph.vue`) - 基于D3.js

## 快速开始

### 1. 安装依赖

项目已包含所需依赖：
- `cytoscape`: ^3.33.1
- `d3`: ^7.9.0

### 2. 基本使用

```vue
<template>
  <!-- Cytoscape版本 -->
  <GraphComponent 
    :data="graphData"
    :attrs="nodeAttrs"
    v-model:panel-visible="showPanel"
    @load-children="handleLoadChildren"
  />
  
  <!-- D3版本 -->
  <D3Graph 
    :data="graphData"
    :attrs="nodeAttrs"
    v-model:panel-visible="showPanel"
    @load-children="handleLoadChildren"
  />
</template>

<script setup>
import GraphComponent from '@/components/graph/index.vue';
import D3Graph from '@/components/graph/d3-graph.vue';

const graphData = ref({
  nodes: [
    {
      id: "1",
      nodeName: "节点1",
      labels: ["unit"],
      attrs: [
        { code: "name", value: "节点1" },
        { code: "type", value: "单位" }
      ]
    }
  ],
  links: [
    { source: "1", target: "2", name: "连接关系" }
  ]
});
</script>
```

## 数据格式

### 节点数据格式
```javascript
{
  id: "唯一标识",
  nodeName: "显示名称",
  labels: ["节点类型"],
  attrs: [
    { code: "属性代码", name: "属性名称", value: "属性值" }
  ]
}
```

### 边数据格式
```javascript
{
  id: "边ID(可选)",
  source: "源节点ID", 
  target: "目标节点ID",
  name: "关系名称"
}
```

### 完整数据示例
```javascript
const sampleData = {
  nodes: [
    {
      id: "1",
      nodeName: "公司总部",
      labels: ["unit"],
      attrs: [
        { code: "name", value: "科技有限公司" },
        { code: "type", value: "总部" },
        { code: "address", value: "北京市朝阳区" }
      ]
    },
    {
      id: "2", 
      nodeName: "张三",
      labels: ["person"],
      attrs: [
        { code: "name", value: "张三" },
        { code: "position", value: "项目经理" },
        { code: "department", value: "技术部" }
      ]
    }
  ],
  links: [
    { source: "1", target: "2", name: "雇佣" }
  ]
};
```

## 组件API

### Props
- `data`: 图形数据对象 `{ nodes: [], links: [] }`
- `attrs`: 当前选中节点的属性数组

### Events
- `update:panel`: 节点右键时触发，传递节点原始数据
- `load-children`: 双击叶子节点时触发，用于动态加载子节点

### Methods (通过ref调用)
- `applyChildren(parentId, payload)`: 添加子节点数据

## 功能特性

### 1. 多种布局模式
- **同心圆布局 (radial)**: 按层级呈同心圆分布
- **垂直树布局 (treeTB)**: 自上而下的树形布局  
- **水平树布局 (treeLR)**: 自左而右的树形布局

### 2. 交互功能
- **拖拽**: 拖动节点改变位置
- **缩放**: 鼠标滚轮缩放视图
- **平移**: 拖拽空白区域平移视图
- **点击高亮**: 点击节点高亮相关节点和边
- **双击展开**: 双击节点展开/折叠子节点或加载新数据
- **右键菜单**: 右键显示节点详细信息

### 3. 工具栏
- **回到中心**: 重置视图到适合大小
- **布局切换**: 切换不同的布局算法
- **全屏模式**: 进入/退出全屏显示

### 4. 动态数据加载
```javascript
// 处理子节点加载
function handleLoadChildren({ id, raw }) {
  // 异步获取子节点数据
  fetchChildrenData(id).then(childData => {
    // 调用组件方法添加子节点
    graphRef.value.applyChildren(id, childData);
  });
}
```

## 样式定制

### Cytoscape版本
通过组件内的样式配置：
```javascript
const style = [
  {
    selector: 'node',
    style: {
      'background-color': '#ff0000',
      'border-width': 2
    }
  }
];
```

### D3版本  
通过CSS类定制：
```scss
:deep(.node circle) {
  fill: #ff0000;
  stroke-width: 2px;
}

:deep(.node.highlight circle) {
  stroke-width: 3px;
  filter: brightness(1.1);
}
```

## 性能优化建议

### 大数据量处理
1. **分批加载**: 初始只加载核心节点，按需加载子节点
2. **虚拟化**: 对于超大图形，考虑只渲染可视区域
3. **简化样式**: 减少复杂的CSS效果和动画

### 内存管理
1. **及时清理**: 组件销毁时清理事件监听器
2. **数据缓存**: 合理缓存已加载的数据
3. **避免内存泄漏**: 注意闭包和循环引用

## 故障排除

### 常见问题

1. **图形不显示**
   - 检查容器是否有高度
   - 确认数据格式正确
   - 查看控制台错误信息

2. **布局异常**
   - 确保节点ID唯一
   - 检查边的source/target是否存在对应节点
   - 尝试重新调用布局方法

3. **交互无响应**
   - 检查事件监听器是否正确绑定
   - 确认组件ref是否正确获取
   - 查看是否有CSS样式冲突

### 调试技巧
```javascript
// 开启调试模式
console.log('图形数据:', graphData.value);
console.log('组件实例:', graphRef.value);

// 监听数据变化
watch(() => graphData.value, (newData) => {
  console.log('数据更新:', newData);
}, { deep: true });
```

## 演示页面

访问 `/d3-graph-demo` 查看D3版本的完整演示，包括：
- 示例数据加载
- 动态节点添加
- 交互功能展示
- 布局切换演示

## 更多资源

- [Cytoscape.js 官方文档](https://js.cytoscape.org/)
- [D3.js 官方文档](https://d3js.org/)
- [Vue3 组合式API](https://vuejs.org/guide/composition-api/)
