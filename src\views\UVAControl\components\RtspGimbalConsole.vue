<template>
  <transition name="fade">
    <div
      v-resize="{
        handles: ['e', 's', 'se'],
        showHandles: false,
        minWidth: 300,
        minHeight: 200,
        maxWidth: 1200,
        maxHeight: 600,
      }"
      v-if="internalVisible"
      ref="panelRef"
      :style="panelStyle"
      class="rtsp-panel"
      @click.stop
    >
      <!-- 头部 -->
      <div class="panel-header draggable-header" @mousedown="handleMouseDown">
        <div class="panel-title">
          <div class="panel-icon">
            <el-icon><VideoCamera /></el-icon>
          </div>
          <div class="title-text">
            <h4>RTSP视频流管理</h4>
            <span class="subtitle">{{ rtspList.length }} 个视频源</span>
          </div>
          <div class="drag-indicator"><span class="drag-dots">⋮⋮</span></div>
        </div>
        <div class="close-btn flex items-center cursor-pointer" @click="handleClose">
          <el-icon size="28"><Close /></el-icon>
        </div>
      </div>

      <!-- 内容 -->
      <div class="panel-content">
        <div v-if="rtspList.length" class="rtsp-list">
          <div v-for="item in rtspList" :key="item.id" class="rtsp-item">
            <div class="rtsp-info">
              <div class="rtsp-name">{{ item.name }}</div>
              <div class="rtsp-urls">
                <div v-if="item.rtspKJG" class="url-item">可见光: {{ item.rtspKJG }}</div>
                <div v-if="item.rtspRCX" class="url-item">热成像: {{ item.rtspRCX }}</div>
              </div>
            </div>
            <div class="rtsp-controls">
              <el-button
                v-if="item.rtspKJG"
                :type="item.playingVisible ? 'danger' : 'primary'"
                size="small"
                :loading="item.loadingVisible"
                @click="togglePlay(item, 'visible')"
                >{{ item.playingVisible ? "停止可见光" : "播放可见光" }}</el-button
              >
              <el-button
                v-if="item.rtspRCX"
                :type="item.playingThermal ? 'danger' : 'primary'"
                size="small"
                :loading="item.loadingThermal"
                @click="togglePlay(item, 'thermal')"
                >{{ item.playingThermal ? "停止热成像" : "播放热成像" }}</el-button
              >
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <el-icon class="empty-icon"><VideoCamera /></el-icon>
          <p>暂无视频流</p>
        </div>

        <!-- 播放区 -->
        <div v-if="playingVideos.length" class="video-section">
          <h5 class="section-title">视频播放</h5>
          <div class="video-grid">
            <div v-for="video in playingVideos" :key="video.id" class="video-container">
              <!-- 全屏时隐藏头（避免占高） -->
              <div class="video-header" v-show="!(isFullscreen && fullscreenVideoId === video.id)">
                <span class="video-title">{{ video.name }} - {{ video.type === "visible" ? "可见光" : "热成像" }}</span>
                <div class="actions">
                  <el-button size="small" @click="enterFullscreen(video.id)">全屏</el-button>
                  <el-button size="small" type="danger" @click="stopVideo(video)">关闭</el-button>
                </div>
              </div>

              <!-- 全屏目标：只包 video + overlay -->
              <div class="player-wrap" :ref="(el) => setWrapRef(video.id, el)">
                <!-- 全屏时叠加云台 -->
                <UavGimbalWidget
                  v-show="isFullscreen && fullscreenVideoId === video.id"
                  class="gimbal-overlay"
                  :azMin="-90"
                  :azMax="90"
                  :elMin="-90"
                  :elMax="90"
                  @set-angles="(a) => onSetAngles(video, a)"
                  @zoom="(z) => onZoom(video, z)"
                  @action="(k) => onAction(video, k)"
                />
                <video
                  :ref="(el) => setVideoRef(video.id, el)"
                  playsinline
                  muted
                  autoplay
                  disablePictureInPicture
                  controlslist="nodownload noplaybackrate noremoteplayback noremoteplayback"
                  class="video-player"
                ></video>
              </div>

              <div v-if="video.error" class="video-error">视频加载失败: {{ video.error }}</div>
            </div>
          </div>
        </div>

        <!-- 底部 -->
        <div class="panel-footer">
          <div class="footer-left">
            <el-button size="small" @click="playAll">全部播放</el-button>
            <el-button size="small" @click="stopAll">全部停止</el-button>
          </div>
          <el-button size="small" @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { computed, ref, nextTick, onUnmounted, watch, onMounted } from "vue";
import { Close, VideoCamera } from "@element-plus/icons-vue";
import flvjs from "flv.js";
import UavGimbalWidget from "../components/UavGimbalWidget.vue";
// import { getFlvURL } from '@/api/spectrumMng/simulation.js' // 用你自己的接口

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  rtspList: { type: Array, default: () => [] },
});
const emit = defineEmits(["update:modelValue"]);

const internalVisible = computed({
  get: () => props.modelValue,
  set: (v) => emit("update:modelValue", v),
});

/* 拖动面板 */
const panelRef = ref(null);
const isDragging = ref(false);
const dragStartX = ref(0),
  dragStartY = ref(0);
const panelStartX = ref(0),
  panelStartY = ref(0);
const panelStyle = computed(() => ({ position: "fixed", zIndex: 1000 }));

function handleMouseDown(e) {
  if (!panelRef.value) return;
  e.preventDefault();
  e.stopPropagation();
  isDragging.value = true;
  dragStartX.value = e.clientX;
  dragStartY.value = e.clientY;
  const rect = panelRef.value.getBoundingClientRect();
  panelStartX.value = rect.left;
  panelStartY.value = rect.top;
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
}
function handleMouseMove(e) {
  if (!isDragging.value || !panelRef.value) return;
  e.preventDefault();
  const dx = e.clientX - dragStartX.value;
  const dy = e.clientY - dragStartY.value;
  let x = panelStartX.value + dx;
  let y = panelStartY.value + dy;
  const r = panelRef.value.getBoundingClientRect();
  x = Math.max(0, Math.min(x, window.innerWidth - r.width));
  y = Math.max(0, Math.min(y, window.innerHeight - r.height));
  panelRef.value.style.left = x + "px";
  panelRef.value.style.top = y + "px";
}
function handleMouseUp() {
  if (!isDragging.value) return;
  isDragging.value = false;
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
}
function setInitialPosition() {
  if (!panelRef.value) return;
  const x = (window.innerWidth - 900) / 2;
  const y = (window.innerHeight - 640) / 2;
  panelRef.value.style.left = x + "px";
  panelRef.value.style.top = y + "px";
}
watch(
  () => props.modelValue,
  async (v) => {
    if (v) await nextTick().then(setInitialPosition);
    else stopAll();
  }
);

/* 播放管理 */
const playingVideos = ref([]); // { id, itemId, name, type, rtspUrl, flvUrl, error }
const videoRefs = ref(new Map());
const wrapRefs = ref(new Map());
const flvPlayers = ref(new Map());

function setVideoRef(id, el) {
  el ? videoRefs.value.set(id, el) : videoRefs.value.delete(id);
}
function setWrapRef(id, el) {
  el ? wrapRefs.value.set(id, el) : wrapRefs.value.delete(id);
}

async function togglePlay(item, type) {
  const playing = type === "visible" ? item.playingVisible : item.playingThermal;
  if (playing) stopVideo({ id: `${item.id}_${type}`, itemId: item.id, type });
  else await playVideo(item, type);
}
async function playVideo(item, type) {
  const loadingKey = type === "visible" ? "loadingVisible" : "loadingThermal";
  const playingKey = type === "visible" ? "playingVisible" : "playingThermal";
  const rtspUrl = type === "visible" ? item.rtspKJG : item.rtspRCX;
  if (item[loadingKey] || !rtspUrl) return;
  item[loadingKey] = true;
  try {
    // const { data } = await getFlvURL({ rtspUrl })
    // const flvUrl = data?.data || data?.msg || data
    const flvUrl = rtspUrl; // demo：直接使用；生产换成后端返回的 FLV 地址
    if (!flvUrl || typeof flvUrl !== "string") throw new Error("后端未返回有效的 FLV 地址");
    const vd = { id: `${item.id}_${type}`, itemId: item.id, name: item.name, type, rtspUrl, flvUrl, error: "" };
    playingVideos.value.push(vd);
    item[playingKey] = true;
    await nextTick();
    await initVideo(vd);
  } catch (err) {
    console.error("视频播放失败:", err);
    const i = playingVideos.value.findIndex((v) => v.id === `${item.id}_${type}`);
    if (i > -1) playingVideos.value[i].error = err.message;
  } finally {
    item[loadingKey] = false;
  }
}

async function initVideo(vd) {
  if (!flvjs.isSupported()) throw new Error("当前浏览器不支持 MSE / flv.js");
  const el = videoRefs.value.get(vd.id);
  if (!el) throw new Error("video 元素未就绪");
  el.controls = false; // 实时预览：隐藏控制条/进度
  const existing = flvPlayers.value.get(vd.id);
  if (existing) await destroyPlayer(vd.id);
  el.src = "";
  el.load();
  await new Promise((r) => setTimeout(r, 80));
  const p = flvjs.createPlayer({
    type: "flv",
    url: vd.flvUrl,
    isLive: true,
    hasVideo: true,
    hasAudio: false,
    enableStashBuffer: false,
    stashInitialSize: 128,
  });
  p.on(flvjs.Events.ERROR, async (type, detail, info) => {
    console.error("FLV 错误:", type, detail, info);
    const i = playingVideos.value.findIndex((v) => v.id === vd.id);
    if (i > -1) playingVideos.value[i].error = `播放错误: ${type}`;
    if (type === flvjs.ErrorTypes.NETWORK_ERROR) {
      setTimeout(async () => {
        try {
          await destroyPlayer(vd.id);
          await initVideo(vd);
        } catch (e) {
          console.error("重连失败", e);
        }
      }, 3000);
    }
  });
  p.attachMediaElement(el);
  flvPlayers.value.set(vd.id, p);
  await p.load();
  setTimeout(async () => {
    try {
      await p.play();
    } catch (e) {
      console.warn("自动播放失败", e);
    }
  }, 120);
}

async function stopVideo(video) {
  await destroyPlayer(video.id);
  const i = playingVideos.value.findIndex((v) => v.id === video.id);
  if (i > -1) playingVideos.value.splice(i, 1);
  const orig = props.rtspList.find((x) => x.id === video.itemId);
  if (orig) {
    const k = video.type === "visible" ? "playingVisible" : "playingThermal";
    const l = video.type === "visible" ? "loadingVisible" : "loadingThermal";
    orig[k] = false;
    orig[l] = false;
  }
}
async function destroyPlayer(id) {
  const p = flvPlayers.value.get(id);
  if (p) {
    try {
      if (!p.paused) p.pause();
      p.unload();
      p.detachMediaElement();
      p.destroy();
      await new Promise((r) => setTimeout(r, 40));
    } catch (e) {
      console.warn("销毁异常", e);
    } finally {
      flvPlayers.value.delete(id);
    }
  }
}
async function playAll() {
  for (const item of props.rtspList) {
    if (item.rtspKJG && !item.playingVisible) await playVideo(item, "visible");
    if (item.rtspRCX && !item.playingThermal) await playVideo(item, "thermal");
  }
}
async function stopAll() {
  await Promise.all([...playingVideos.value].map((v) => stopVideo(v)));
}
async function handleClose() {
  await stopAll();
  internalVisible.value = false;
}

/* 全屏 & 云台联动：让 player-wrap 进入原生全屏 */
const fullscreenVideoId = ref(null);
const isFullscreen = ref(false);

function enterFullscreen(videoId) {
  const wrap = wrapRefs.value.get(videoId);
  if (!wrap) return;
  if (wrap.requestFullscreen) wrap.requestFullscreen();
  else if (wrap.webkitRequestFullscreen) wrap.webkitRequestFullscreen();
  else if (wrap.msRequestFullscreen) wrap.msRequestFullscreen();
  fullscreenVideoId.value = videoId;
}

function handleFsChange() {
  isFullscreen.value = !!document.fullscreenElement;
  if (!isFullscreen.value) fullscreenVideoId.value = null;
}
onMounted(() => {
  document.addEventListener("fullscreenchange", handleFsChange);
  document.addEventListener("webkitfullscreenchange", handleFsChange);
  document.addEventListener("msfullscreenchange", handleFsChange);
});
onUnmounted(() => {
  document.removeEventListener("fullscreenchange", handleFsChange);
  document.removeEventListener("webkitfullscreenchange", handleFsChange);
  document.removeEventListener("msfullscreenchange", handleFsChange);
});

/* 云台回调（替换为你的下发接口） */
function onSetAngles(video, { azimuth, elevation }) {
  console.log("[SET-ANGLES]", { device: video.itemId, azimuth, elevation });
}
function onZoom(video, { zoom }) {
  console.log("[ZOOM]", { device: video.itemId, zoom });
}
function onAction(video, key) {
  console.log("[ACTION]", { device: video.itemId, key });
}
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.rtsp-panel {
  width: 900px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.65);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: move;
}
.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}
.panel-icon {
  color: #00d4ff;
  font-size: 16px;
}
.title-text h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}
.title-text .subtitle {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}
.drag-indicator {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: 8px;
}
.drag-dots {
  color: rgba(255, 255, 255, 0.4);
  font-size: 16px;
  line-height: 1;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}
.rtsp-list .rtsp-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  margin-bottom: 8px;
}
.rtsp-item .rtsp-info {
  flex: 1;
}
.rtsp-name {
  font-weight: 500;
  margin-bottom: 4px;
}
.rtsp-urls .url-item {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}
.rtsp-controls {
  display: flex;
  gap: 8px;
}

.video-section {
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}
.section-title {
  margin: 0 0 12px 0;
  font-size: 13px;
  font-weight: 600;
  color: #00d4ff;
}
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
  gap: 12px;
}
.video-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}
.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
}
.video-title {
  font-size: 11px;
  font-weight: 500;
}

/* 非全屏：卡片内固定高度 */
.player-wrap {
  position: relative;
}
.video-player {
  width: 100%;
  height: 280px;
  object-fit: contain;
  background: #000;
  z-index: 1;
}

/* ===== 全屏的是 player-wrap 本身 ===== */
.player-wrap:fullscreen,
.player-wrap:-webkit-full-screen,
.player-wrap:-ms-fullscreen {
  position: fixed;
  inset: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
}
.player-wrap:fullscreen .video-player,
.player-wrap:-webkit-full-screen .video-player,
.player-wrap:-ms-fullscreen .video-player {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
  z-index: 1;
}
/* 全屏叠加云台（占满以便内部自定居中/定位） */
.gimbal-overlay {
  position: absolute;
  inset: 0;
  z-index: 9;
  pointer-events: none;
}
.gimbal-overlay * {
  pointer-events: auto;
}
.player-wrap:fullscreen .gimbal-overlay,
.player-wrap:-webkit-full-screen .gimbal-overlay,
.player-wrap:-ms-fullscreen .gimbal-overlay {
  z-index: 20;
}

.video-error {
  padding: 8px 12px;
  color: #ff6b6b;
  font-size: 11px;
  background: rgba(255, 107, 107, 0.1);
}

.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.footer-left {
  display: flex;
  gap: 8px;
}
</style>
