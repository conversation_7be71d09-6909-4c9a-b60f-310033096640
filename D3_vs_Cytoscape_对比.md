# D3.js vs Cytoscape.js 图形组件对比

## 概述

本项目提供了两个功能相同的图形可视化组件：
- **原版**：基于 Cytoscape.js (`src/components/graph/index.vue`)
- **D3版本**：基于 D3.js (`src/components/graph/d3-graph.vue`)

## 功能对比

### 相同功能
✅ **布局算法**
- 同心圆布局 (radial)
- 垂直树布局 (treeTB) 
- 水平树布局 (treeLR)

✅ **交互功能**
- 节点拖拽
- 视图缩放和平移
- 节点点击高亮
- 双击展开/折叠
- 右键显示详情

✅ **动态数据**
- 实时数据更新
- 动态加载子节点
- 节点隐藏/显示

✅ **视觉效果**
- 节点颜色映射
- 高亮和淡化效果
- 边标签显示

✅ **工具栏**
- 回到中心点
- 布局切换
- 全屏模式

## 技术差异

### Cytoscape.js 版本
```javascript
// 使用 Cytoscape 的声明式API
cy = cytoscape({
  container: containerEl.value,
  elements: elements,
  style: styleConfig,
  layout: layoutConfig
});
```

**优势：**
- 🎯 专门为图形可视化设计
- 📦 开箱即用的布局算法
- 🔧 丰富的配置选项
- 📚 完善的文档和社区

**劣势：**
- 📈 较大的包体积 (~500KB)
- 🔒 定制化程度有限
- 🎨 样式系统相对固化

### D3.js 版本
```javascript
// 使用 D3 的数据驱动方式
const nodeSelection = svg.selectAll('.node')
  .data(nodes, d => d.id);
  
nodeSelection.enter()
  .append('g')
  .attr('class', 'node');
```

**优势：**
- 🎨 极高的定制化程度
- 📊 强大的数据处理能力
- 🔧 灵活的动画和过渡
- 📦 模块化，按需引入
- 🌐 Web标准，无厂商锁定

**劣势：**
- 📈 学习曲线较陡
- ⏰ 开发时间较长
- 🔧 需要手动实现更多功能

## 性能对比

### 渲染性能
- **Cytoscape**: 内部优化，适合中等规模图形 (< 1000节点)
- **D3**: 直接操作DOM，可针对性优化，适合大规模图形

### 内存占用
- **Cytoscape**: 较高，包含完整的图形引擎
- **D3**: 较低，只加载需要的模块

### 包体积
- **Cytoscape**: ~500KB (gzipped ~150KB)
- **D3**: ~200KB (按需引入可更小)

## 使用建议

### 选择 Cytoscape.js 当：
- 🚀 需要快速开发原型
- 📊 图形复杂度中等
- 👥 团队对图形库经验有限
- 🎯 需要稳定的现成解决方案

### 选择 D3.js 当：
- 🎨 需要高度定制化
- 📈 处理大规模数据
- 🔧 需要复杂的动画效果
- 📦 对包体积有严格要求
- 🌐 希望更好的Web标准兼容性

## 迁移指南

### 从 Cytoscape 迁移到 D3

1. **数据格式保持不变**
   ```javascript
   // 两个版本使用相同的数据格式
   const data = {
     nodes: [{ id, label, type, ... }],
     links: [{ source, target, label, ... }]
   };
   ```

2. **API接口兼容**
   ```javascript
   // 相同的组件接口
   <GraphComponent 
     :data="graphData"
     :attrs="attrs"
     @load-children="handleLoadChildren"
   />
   ```

3. **样式调整**
   ```scss
   // D3版本使用CSS类而非JS配置
   :deep(.node.highlight) {
     stroke-width: 3px;
   }
   ```

## 示例代码

### 基本使用
```vue
<template>
  <D3Graph 
    :data="graphData"
    :attrs="nodeAttrs"
    v-model:panel-visible="showPanel"
    @load-children="loadChildren"
  />
</template>

<script setup>
import D3Graph from '@/components/graph/d3-graph.vue';

const graphData = ref({
  nodes: [
    { id: '1', nodeName: '节点1', labels: ['unit'] },
    { id: '2', nodeName: '节点2', labels: ['person'] }
  ],
  links: [
    { source: '1', target: '2', name: '连接' }
  ]
});
</script>
```

## 总结

两个版本都能满足基本的图形可视化需求，选择哪个主要取决于：

- **开发效率 vs 定制化程度**
- **包体积 vs 功能完整性** 
- **学习成本 vs 长期维护性**

对于大多数业务场景，Cytoscape版本已经足够。如果需要特殊的视觉效果或性能优化，D3版本提供了更大的灵活性。
