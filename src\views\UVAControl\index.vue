<template>
  <UavGimbalWidget
    :azMin="-90"
    :azMax="90"
    :elMin="-90"
    :elMax="90"
    @set-angles="onAngles"
    @zoom="onZoom"
    @action="onAction"
  />
</template>

<script setup>
import UavGimbalWidget from "./components/UavGimbalWidget.vue";

function onZoom({ zoom }) {
  console.log("缩放:", zoom);
  // ws.send(JSON.stringify({ type: 'zoom', zoom }))
}

function onAction(key) {
  console.log("动作:", key);
  // 'video'|'photo'|'display'|'stream'|'settings'|'stabilize'|'grid'|'osd'|'record'|'download'|'more'
}

function onAngles({ azimuth, elevation }) {
  console.log("角度更新:", {
    方位角: azimuth.toFixed(2) + "°",
    俯仰角: elevation.toFixed(2) + "°",
  });
}
</script>

<style></style>
