<template>
  <div id="map" v-loading="loading"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from "vue";
import "ol/ol.css";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import TileLayer from "ol/layer/Tile";
import XYZ from "ol/source/XYZ";
import { Map, View, Feature } from "ol";
import { fromLonLat } from "ol/proj";
import { Style, Text, Fill, Stroke, Circle } from "ol/style";
import { Point } from "ol/geom";
import { ElMessage } from "element-plus";
import gcj02Mecator from "@/utils/gcj02Mecator";

const map = ref(null);
const loading = ref(false); // 加载状态
let pointLayer = null; // 点图层
let lineLayer = null; // 线图层
const pointsArr = reactive([
  {
    name: "主机101",
    value: [119.421003, 32.393159],
  },
  {
    name: "主机102",
    value: [119.6, 32.393159],
  },
  {
    name: "主机103",
    value: [118.421003, 32.393159],
  },
  {
    name: "主机104",
    value: [117.421003, 32.393159],
  },
]);

const initMap = () => {
  const mapLayerSource = new XYZ({
    projection: gcj02Mecator,
    url: "http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}",
  });

  map.value = new Map({
    target: "map",
    layers: [
      new TileLayer({
        source: mapLayerSource,
        zIndex: 0,
      }),
    ],
    view: new View({
      projection: "EPSG:3857",
      center: fromLonLat([119.421003, 32.393159]),
      zoom: 12,
    }),
  });

  // 添加点图层
  pointLayer = new VectorLayer({
    source: new VectorSource(),
  });
  map.value.addLayer(pointLayer);

  // 添加线图层
  lineLayer = new VectorLayer({
    source: new VectorSource(),
  });
  map.value.addLayer(lineLayer);

  // 添加地图点击事件
  map.value.on("click", (event) => {
    console.log("点击坐标:", event.coordinate);
    const [lon, lat] = event.coordinate;
    console.log("点击坐标:", lon, lat);
  });
};

const addPoints = (pointInfo) => {
  const startPoint = pointInfo.value; // [lon, lat]
  const pointFeature = new Feature({
    geometry: new Point(fromLonLat(startPoint)),
  });
  pointFeature.setStyle(
    new Style({
      // 圆点样式
      image: new Circle({
        radius: 6, // 圆点半径
        fill: new Fill({
          color: "blue", // 圆点填充色
        }),
        stroke: new Stroke({
          color: "white", // 圆点边框色
          width: 2, // 边框宽度
        }),
      }),
      // 文本样式（保持你原有的文字设置）
      text: new Text({
        text: pointInfo.name,
        fill: new Fill({
          color: "red",
        }),
        offsetY: 15, // 文字在圆点下方15px处，避免重叠
        textAlign: "center", // 文字居中对齐
      }),
    })
  );
  pointLayer.getSource().addFeature(pointFeature);
};

onMounted(async () => {
  try {
    initMap();
    pointsArr.forEach((item) => {
      addPoints(item);
    });
  } catch (error) {
    ElMessage.error("初始化地图失败");
    console.error("初始化错误:", error);
  }
});

onUnmounted(() => {
  if (map.value) {
    map.value.setTarget(null);
    map.value = null;
  }
});
</script>

<style scoped>
#map {
  height: 100%;
  width: 100%;
}

.elevation-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.8);
  width: 100px;
  padding: 10px;
  border-radius: 4px;
  z-index: 1000;
}
</style>
