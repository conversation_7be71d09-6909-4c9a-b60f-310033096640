<!-- UavGimbalSetpoint.vue（按时间积分的增量控制：100ms=1°） -->
<template>
  <div class="uav-widget" @contextmenu.prevent>
    <div
      ref="padRef"
      class="stick-pad"
      :style="{ width: size + 'px', height: size + 'px' }"
      @pointerdown="onPadDown"
      @wheel.prevent="onWheelZoom"
      :class="{ dragging: dragging }"
      title="按下后拖拽；按时间积分角度；滚轮缩放"
    >
      <div class="ring"></div>
      <div class="cross cross-x"></div>
      <div class="cross cross-y"></div>
      <div class="thumb" :style="thumbStyle"></div>
    </div>

    <!-- 角度显示（当前绝对角） -->
    <div class="angle-display">
      <div class="angle-item">
        <span class="angle-label">方位角:</span><span class="angle-value">{{ currentAngles.azimuth.toFixed(1) }}°</span>
      </div>
      <div class="angle-item">
        <span class="angle-label">俯仰角:</span
        ><span class="angle-value">{{ currentAngles.elevation.toFixed(1) }}°</span>
      </div>
    </div>

    <div class="side-buttons">
      <el-button @click="zoomIn" :icon="Plus" circle />
      <el-button @click="zoomOut" :icon="Minus" circle />
    </div>

    <div class="bottom-bar" v-if="showBottomBar">
      <el-tooltip v-for="b in bottomBtns" :key="b.key" :content="b.title" placement="top">
        <el-button circle @click="emitAction(b.key)" :icon="b.icon" />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, reactive, ref, computed, nextTick, defineExpose, watch } from "vue";
import { CameraFilled, Plus, Minus } from "@element-plus/icons-vue";

const emit = defineEmits(["set-angles", "zoom", "action"]);

// ====== Props（新增了速度/发射频率等配置）======
const props = defineProps({
  size: { type: Number, default: 160 },
  deadzone: { type: Number, default: 0.12 },

  // 角度范围
  azMin: { type: Number, default: -90 },
  azMax: { type: Number, default: 90 },
  elMin: { type: Number, default: -90 },
  elMax: { type: Number, default: 90 },

  // ——时间积分参数——
  // 100ms=1° → 10°/s；可按需修改
  maxDegPerSec: { type: Number, default: 10 },
  // 是否用摇杆半径调节速度（r∈[0,1]），true=越靠外速度越快；false=恒速
  useMagnitude: { type: Boolean, default: true },
  // 半径指数，>1 让中心更细腻：速度 ∝ r^radialExponent
  radialExponent: { type: Number, default: 1 },

  // 事件节流（向外 emit 的最大频率）
  emitHz: { type: Number, default: 30 },

  // 可选：设备回传的实时角度（用于对齐显示/校正漂移）
  telemetryAngles: { type: Object, default: null }, // { azimuth, elevation }

  // 变焦
  zoom: { type: Number, default: 1 },
  zoomMin: { type: Number, default: 1 },
  zoomMax: { type: Number, default: 20 },
  zoomStep: { type: Number, default: 0.1 },

  showBottomBar: { type: Boolean, default: false },
  // 松手是否让拇指UI回到中心（不改变当前角）
  recenterOnRelease: { type: Boolean, default: true },
});

// ====== State ======
const padRef = ref(null);
const center = reactive({ x: 0, y: 0, r: 0 });
const thumb = reactive({ x: 0, y: 0 });
const norm = reactive({ x: 0, y: 0 }); // [-1,1]
const zoomVal = ref(props.zoom);
const dragging = ref(false);

// 当前绝对角（作为累积结果，也可被遥测覆盖校正）
const currentAngles = reactive({ azimuth: 0, elevation: 0 });

let activePointerId = null;
let rafId = null;
let lastTick = 0;
let lastEmitAt = 0;

// UI
const bottomBtns = [
  { key: "video", title: "视频", icon: CameraFilled },
  { key: "photo", title: "拍照", icon: CameraFilled },
  { key: "display", title: "显示", icon: CameraFilled },
  { key: "stream", title: "串流", icon: CameraFilled },
  { key: "settings", title: "设置", icon: CameraFilled },
  { key: "stabilize", title: "防抖", icon: CameraFilled },
  { key: "grid", title: "网格", icon: CameraFilled },
  { key: "osd", title: "OSD", icon: CameraFilled },
  { key: "record", title: "录像", icon: CameraFilled },
  { key: "download", title: "下载", icon: CameraFilled },
];

const thumbStyle = computed(() => ({ transform: `translate(${thumb.x - 17}px, ${thumb.y - 17}px)` }));

// ====== Utils ======
const clamp = (n, min, max) => Math.min(max, Math.max(min, n));
function computeCenter() {
  if (!padRef.value) return;
  const rect = padRef.value.getBoundingClientRect();
  center.x = rect.left + rect.width / 2;
  center.y = rect.top + rect.height / 2;
  center.r = Math.min(rect.width, rect.height) / 2 - 20;
}
function applyDead(nx, ny) {
  const r = Math.hypot(nx, ny);
  if (r < props.deadzone) return { x: 0, y: 0 };
  const k = (r - props.deadzone) / (1 - props.deadzone);
  const scale = k / (r || 1);
  return { x: nx * scale, y: ny * scale };
}
function pixelToNorm(dx, dy) {
  const nx = clamp(dx / center.r, -1, 1);
  const ny = clamp(dy / center.r, -1, 1);
  return applyDead(nx, ny);
}

// ====== Pointer（按下后才拖拽）======
function onPadDown(e) {
  e.preventDefault();
  activePointerId = e.pointerId;
  e.currentTarget.setPointerCapture(e.pointerId);
  dragging.value = true;

  // 基于遥测对齐当前角（可选）
  if (props.telemetryAngles && isFinite(props.telemetryAngles.azimuth) && isFinite(props.telemetryAngles.elevation)) {
    currentAngles.azimuth = clamp(props.telemetryAngles.azimuth, props.azMin, props.azMax);
    currentAngles.elevation = clamp(props.telemetryAngles.elevation, props.elMin, props.elMax);
  }

  // 初始化手指位置并启动积分循环
  updateFromPointer(e);
  startIntegrateLoop();
}
function onPadMove(e) {
  if (!dragging.value || e.pointerId !== activePointerId) return;
  updateFromPointer(e);
}
function onPadUp(e) {
  if (e.pointerId !== activePointerId) return;
  stopIntegrateLoop();
  dragging.value = false;
  activePointerId = null;
  e.currentTarget.releasePointerCapture(e.pointerId);
  if (props.recenterOnRelease) backToCenter(); // 仅UI回中
}
function onWindowPointerUp() {
  if (!dragging.value) return;
  stopIntegrateLoop();
  dragging.value = false;
  activePointerId = null;
  if (props.recenterOnRelease) backToCenter();
}

function updateFromPointer(e) {
  computeCenter();
  const dx = e.clientX - center.x;
  const dy = e.clientY - center.y;
  const L = Math.hypot(dx, dy);
  const rate = L > center.r ? center.r / L : 1;
  thumb.x = dx * rate;
  thumb.y = dy * rate;

  const n = pixelToNorm(thumb.x, thumb.y);
  norm.x = n.x;
  norm.y = n.y;
}

// ====== 时间积分循环（核心逻辑）======
function startIntegrateLoop() {
  cancelAnimationFrame(rafId);
  lastTick = performance.now();
  lastEmitAt = 0;

  const frame = (now) => {
    const dt = now - lastTick;
    lastTick = now;

    // 归一向量 & 半径
    const nx = norm.x,
      ny = norm.y;
    const r = Math.hypot(nx, ny);

    if (r > 0) {
      // 单位方向（水平→方位角，向上为正→俯仰角）
      const ux = nx / r;
      const uy = -ny / r;

      // 速度 = 基准(°/s) × 半径系数（可关闭）
      const radiusFactor = props.useMagnitude ? Math.pow(r, props.radialExponent) : 1;
      const degPerSec = props.maxDegPerSec * radiusFactor;

      // 本帧角度步进
      const degStep = degPerSec * (dt / 1000); // dt ms → s

      // 分配到方位角/俯仰角
      let newAz = currentAngles.azimuth + degStep * ux;
      let newEl = currentAngles.elevation + degStep * uy;

      // 夹紧到 [-90,90]
      newAz = clamp(newAz, props.azMin, props.azMax);
      newEl = clamp(newEl, props.elMin, props.elMax);

      currentAngles.azimuth = newAz;
      currentAngles.elevation = newEl;

      // 节流触发 emit
      if (now - lastEmitAt >= 1000 / props.emitHz) {
        emit("set-angles", { azimuth: currentAngles.azimuth, elevation: currentAngles.elevation });
        lastEmitAt = now;
      }
    }

    rafId = requestAnimationFrame(frame);
  };

  rafId = requestAnimationFrame(frame);
}
function stopIntegrateLoop() {
  cancelAnimationFrame(rafId);
  // 结束时补发一次，保证落点已通知
  emit("set-angles", { azimuth: currentAngles.azimuth, elevation: currentAngles.elevation });
}

function backToCenter() {
  const sx = thumb.x,
    sy = thumb.y,
    snx = norm.x,
    sny = norm.y;
  const t0 = performance.now();
  const step = (now) => {
    const t = clamp((now - t0) / 120, 0, 1);
    const ease = 1 - Math.pow(1 - t, 3);
    thumb.x = sx * (1 - ease);
    thumb.y = sy * (1 - ease);
    norm.x = snx * (1 - ease);
    norm.y = sny * (1 - ease);
    if (t < 1) requestAnimationFrame(step);
  };
  requestAnimationFrame(step);
}

// ====== Zoom / Buttons ======
function onWheelZoom(e) {
  setZoom(zoomVal.value + (e.deltaY > 0 ? -props.zoomStep : props.zoomStep));
}
function setZoom(z) {
  zoomVal.value = clamp(+z.toFixed(2), props.zoomMin, props.zoomMax);
  emit("zoom", { zoom: zoomVal.value });
}
function zoomIn() {
  setZoom(zoomVal.value + props.zoomStep);
}
function zoomOut() {
  setZoom(zoomVal.value - props.zoomStep);
}
function emitAction(key) {
  emit("action", key);
}

// 外部 API
function centerStick() {
  thumb.x = thumb.y = 0;
  norm.x = norm.y = 0;
  if (props.recenterOnRelease) backToCenter();
}
function adoptAngles(azimuth, elevation) {
  currentAngles.azimuth = clamp(azimuth, props.azMin, props.azMax);
  currentAngles.elevation = clamp(elevation, props.elMin, props.elMax);
}
defineExpose({ centerStick, zoomIn, zoomOut, adoptAngles });

// 遥测同步（可选）：设备回传角度 → 覆盖当前显示/状态，抹平累积误差
watch(
  () => props.telemetryAngles,
  (v) => {
    if (v && isFinite(v.azimuth) && isFinite(v.elevation)) {
      currentAngles.azimuth = clamp(v.azimuth, props.azMin, props.azMax);
      currentAngles.elevation = clamp(v.elevation, props.elMin, props.elMax);
    }
  },
  { deep: true }
);

// Lifecycle
onMounted(() => {
  nextTick(() => {
    computeCenter();
    window.addEventListener("resize", computeCenter);
    const pad = padRef.value;
    pad.addEventListener("pointermove", onPadMove);
    pad.addEventListener("pointerup", onPadUp);
    pad.addEventListener("pointercancel", onPadUp);
    pad.addEventListener("dblclick", centerStick);
    window.addEventListener("pointerup", onWindowPointerUp);
  });
});
onBeforeUnmount(() => {
  stopIntegrateLoop();
  window.removeEventListener("resize", computeCenter);
  window.removeEventListener("pointerup", onWindowPointerUp);
  const pad = padRef.value;
  if (pad) {
    pad.removeEventListener("pointermove", onPadMove);
    pad.removeEventListener("pointerup", onPadUp);
    pad.removeEventListener("pointercancel", onPadUp);
  }
});
</script>

<style scoped>
.uav-widget {
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
}
.stick-pad {
  position: absolute;
  bottom: 50%;
  left: 20px;
  transform: translateY(50%);
  border-radius: 50%;
  background: #0b0b0b;
  touch-action: none;
  box-shadow: inset 0 0 0 2px rgba(100, 80, 70, 0.6), 0 10px 30px rgba(0, 0, 0, 0.45);
  cursor: grab;
}
.stick-pad.dragging {
  cursor: grabbing;
}
.ring {
  position: absolute;
  inset: 12px;
  border-radius: 50%;
  border: 2px solid rgba(70, 200, 200, 0.35);
}
.cross {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.cross-x {
  width: 76%;
  border-top: 1px dashed rgba(220, 220, 220, 0.2);
}
.cross-y {
  height: 76%;
  border-left: 1px dashed rgba(220, 220, 220, 0.2);
}
.thumb {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 34px;
  height: 34px;
  transform: translate(-17px, -17px);
  border-radius: 50%;
  background: radial-gradient(16px circle at 35% 35%, #8a5140, #4a2c22 70%);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.45), inset 0 0 0 2px rgba(255, 255, 255, 0.06);
  pointer-events: none;
}
.angle-display {
  position: absolute;
  bottom: 61%;
  left: 40px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: #fff;
  white-space: nowrap;
  backdrop-filter: blur(4px);
}
.angle-item {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}
.angle-label {
  color: #ccc;
}
.angle-value {
  color: #4fc3f7;
  font-weight: 500;
  min-width: 48px;
  text-align: right;
}
.side-buttons {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.bottom-bar {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 54px;
  display: flex;
  gap: 12px;
}
:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}
</style>
