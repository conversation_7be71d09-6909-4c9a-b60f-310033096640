/**
 * 图形组件测试数据生成器
 * 用于生成各种测试场景的图形数据
 */

// 基础测试数据
export const basicTestData = {
  nodes: [
    {
      id: "center",
      nodeName: "中心节点",
      labels: ["unit"],
      attrs: [
        { code: "name", value: "中心节点" },
        { code: "type", value: "核心单位" },
        { code: "level", value: "0" }
      ]
    },
    {
      id: "person1",
      nodeName: "张三",
      labels: ["person"],
      attrs: [
        { code: "name", value: "张三" },
        { code: "position", value: "项目经理" },
        { code: "department", value: "技术部" }
      ]
    },
    {
      id: "person2",
      nodeName: "李四",
      labels: ["person"],
      attrs: [
        { code: "name", value: "李四" },
        { code: "position", value: "开发工程师" },
        { code: "department", value: "技术部" }
      ]
    },
    {
      id: "train1",
      nodeName: "Vue3培训",
      labels: ["train"],
      attrs: [
        { code: "name", value: "Vue3培训" },
        { code: "duration", value: "3个月" },
        { code: "status", value: "进行中" }
      ]
    }
  ],
  links: [
    { source: "center", target: "person1", name: "管理" },
    { source: "center", target: "person2", name: "管理" },
    { source: "person1", target: "train1", name: "负责" },
    { source: "person2", target: "train1", name: "参与" }
  ]
};

// 复杂层级数据
export const hierarchicalTestData = {
  nodes: [
    // 第0层 - 根节点
    { id: "root", nodeName: "总公司", labels: ["unit"], attrs: [{ code: "name", value: "总公司" }] },
    
    // 第1层 - 部门
    { id: "tech", nodeName: "技术部", labels: ["unit"], attrs: [{ code: "name", value: "技术部" }] },
    { id: "sales", nodeName: "销售部", labels: ["unit"], attrs: [{ code: "name", value: "销售部" }] },
    { id: "hr", nodeName: "人事部", labels: ["unit"], attrs: [{ code: "name", value: "人事部" }] },
    
    // 第2层 - 团队
    { id: "frontend", nodeName: "前端团队", labels: ["unit"], attrs: [{ code: "name", value: "前端团队" }] },
    { id: "backend", nodeName: "后端团队", labels: ["unit"], attrs: [{ code: "name", value: "后端团队" }] },
    { id: "sales_team1", nodeName: "销售一组", labels: ["unit"], attrs: [{ code: "name", value: "销售一组" }] },
    
    // 第3层 - 人员
    { id: "dev1", nodeName: "前端开发1", labels: ["person"], attrs: [{ code: "name", value: "王五" }] },
    { id: "dev2", nodeName: "前端开发2", labels: ["person"], attrs: [{ code: "name", value: "赵六" }] },
    { id: "dev3", nodeName: "后端开发1", labels: ["person"], attrs: [{ code: "name", value: "孙七" }] },
    { id: "sales1", nodeName: "销售员1", labels: ["person"], attrs: [{ code: "name", value: "周八" }] }
  ],
  links: [
    // 第0层到第1层
    { source: "root", target: "tech", name: "包含" },
    { source: "root", target: "sales", name: "包含" },
    { source: "root", target: "hr", name: "包含" },
    
    // 第1层到第2层
    { source: "tech", target: "frontend", name: "包含" },
    { source: "tech", target: "backend", name: "包含" },
    { source: "sales", target: "sales_team1", name: "包含" },
    
    // 第2层到第3层
    { source: "frontend", target: "dev1", name: "包含" },
    { source: "frontend", target: "dev2", name: "包含" },
    { source: "backend", target: "dev3", name: "包含" },
    { source: "sales_team1", target: "sales1", name: "包含" }
  ]
};

// 网状结构数据
export const networkTestData = {
  nodes: [
    { id: "A", nodeName: "节点A", labels: ["unit"], attrs: [{ code: "name", value: "节点A" }] },
    { id: "B", nodeName: "节点B", labels: ["person"], attrs: [{ code: "name", value: "节点B" }] },
    { id: "C", nodeName: "节点C", labels: ["train"], attrs: [{ code: "name", value: "节点C" }] },
    { id: "D", nodeName: "节点D", labels: ["other"], attrs: [{ code: "name", value: "节点D" }] },
    { id: "E", nodeName: "节点E", labels: ["unit"], attrs: [{ code: "name", value: "节点E" }] },
    { id: "F", nodeName: "节点F", labels: ["person"], attrs: [{ code: "name", value: "节点F" }] }
  ],
  links: [
    { source: "A", target: "B", name: "连接1" },
    { source: "A", target: "C", name: "连接2" },
    { source: "B", target: "D", name: "连接3" },
    { source: "C", target: "D", name: "连接4" },
    { source: "C", target: "E", name: "连接5" },
    { source: "D", target: "F", name: "连接6" },
    { source: "E", target: "F", name: "连接7" },
    { source: "F", target: "A", name: "连接8" } // 形成环
  ]
};

// 生成随机测试数据
export function generateRandomData(nodeCount = 20, linkDensity = 0.3) {
  const nodeTypes = ['unit', 'person', 'train', 'other'];
  const relationTypes = ['管理', '包含', '连接', '协作', '负责'];
  
  const nodes = [];
  const links = [];
  
  // 生成节点
  for (let i = 0; i < nodeCount; i++) {
    const type = nodeTypes[Math.floor(Math.random() * nodeTypes.length)];
    nodes.push({
      id: `node_${i}`,
      nodeName: `${type}_${i}`,
      labels: [type],
      attrs: [
        { code: "name", value: `${type}_${i}` },
        { code: "type", value: type },
        { code: "index", value: i.toString() }
      ]
    });
  }
  
  // 生成边
  const maxLinks = Math.floor(nodeCount * (nodeCount - 1) * linkDensity / 2);
  const usedPairs = new Set();
  
  for (let i = 0; i < maxLinks; i++) {
    let source, target;
    let pairKey;
    
    do {
      source = Math.floor(Math.random() * nodeCount);
      target = Math.floor(Math.random() * nodeCount);
      pairKey = `${Math.min(source, target)}-${Math.max(source, target)}`;
    } while (source === target || usedPairs.has(pairKey));
    
    usedPairs.add(pairKey);
    
    const relation = relationTypes[Math.floor(Math.random() * relationTypes.length)];
    links.push({
      source: `node_${source}`,
      target: `node_${target}`,
      name: relation
    });
  }
  
  return { nodes, links };
}

// 性能测试数据
export function generateLargeData(nodeCount = 100) {
  return generateRandomData(nodeCount, 0.1);
}

// 空数据
export const emptyTestData = {
  nodes: [],
  links: []
};

// 单节点数据
export const singleNodeData = {
  nodes: [
    {
      id: "single",
      nodeName: "单独节点",
      labels: ["unit"],
      attrs: [
        { code: "name", value: "单独节点" },
        { code: "description", value: "这是一个没有连接的节点" }
      ]
    }
  ],
  links: []
};

// 测试数据集合
export const testDataSets = {
  basic: basicTestData,
  hierarchical: hierarchicalTestData,
  network: networkTestData,
  empty: emptyTestData,
  single: singleNodeData,
  random: () => generateRandomData(),
  large: () => generateLargeData()
};

// 获取测试数据
export function getTestData(type = 'basic') {
  const data = testDataSets[type];
  return typeof data === 'function' ? data() : data;
}

// 验证数据格式
export function validateGraphData(data) {
  const errors = [];
  
  if (!data || typeof data !== 'object') {
    errors.push('数据必须是对象');
    return errors;
  }
  
  if (!Array.isArray(data.nodes)) {
    errors.push('nodes必须是数组');
  } else {
    data.nodes.forEach((node, index) => {
      if (!node.id) {
        errors.push(`节点${index}缺少id`);
      }
      if (!node.nodeName && !node.label) {
        errors.push(`节点${node.id || index}缺少显示名称`);
      }
    });
  }
  
  if (!Array.isArray(data.links)) {
    errors.push('links必须是数组');
  } else {
    const nodeIds = new Set(data.nodes?.map(n => n.id) || []);
    data.links.forEach((link, index) => {
      if (!link.source) {
        errors.push(`边${index}缺少source`);
      } else if (!nodeIds.has(link.source)) {
        errors.push(`边${index}的source节点不存在: ${link.source}`);
      }
      
      if (!link.target) {
        errors.push(`边${index}缺少target`);
      } else if (!nodeIds.has(link.target)) {
        errors.push(`边${index}的target节点不存在: ${link.target}`);
      }
    });
  }
  
  return errors;
}
